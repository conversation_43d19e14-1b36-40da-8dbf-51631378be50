// Simple test to verify the financial settings endpoint
const fetch = require('node-fetch');

async function testFinancialSettings() {
  try {
    console.log('Testing financial settings endpoint...');
    
    const response = await fetch('http://localhost:5000/api/settings/financial');
    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('✅ Financial settings endpoint working correctly');
      console.log('Platform Commission:', data.data.platformCommissionPercentage + '%');
      console.log('Stripe Processing Fee:', data.data.stripeProcessingFeePercentage + '%');
      console.log('Stripe Fixed Fee:', '$' + data.data.stripeFixedFee);
    } else {
      console.log('❌ Financial settings endpoint returned error:', data.message);
    }
  } catch (error) {
    console.log('❌ Error testing financial settings endpoint:', error.message);
  }
}

// Test fee calculation
function testFeeCalculation() {
  console.log('\nTesting fee calculation logic...');
  
  const amount = 100;
  const settings = {
    platformCommissionPercentage: 5,
    stripeProcessingFeePercentage: 2.9,
    stripeFixedFee: 0.30
  };
  
  const platformCommission = amount * (settings.platformCommissionPercentage / 100);
  const stripePercentageFee = amount * (settings.stripeProcessingFeePercentage / 100);
  const stripeTotalFee = stripePercentageFee + settings.stripeFixedFee;
  const sellerEarningsBeforeStripeFees = amount - platformCommission;
  const finalSellerEarnings = sellerEarningsBeforeStripeFees - stripeTotalFee;
  
  console.log('For $100 transaction:');
  console.log('- Original Amount: $' + amount.toFixed(2));
  console.log('- Platform Commission (5%): $' + platformCommission.toFixed(2));
  console.log('- Stripe Fee (2.9% + $0.30): $' + stripeTotalFee.toFixed(2));
  console.log('- Seller Earnings Before Stripe: $' + sellerEarningsBeforeStripeFees.toFixed(2));
  console.log('- Final Seller Earnings: $' + finalSellerEarnings.toFixed(2));
  console.log('- Buyer Pays: $' + amount.toFixed(2));
  
  const totalFeesDeducted = platformCommission + stripeTotalFee;
  console.log('- Total Fees Deducted: $' + totalFeesDeducted.toFixed(2));
  console.log('- Seller Receives: ' + ((finalSellerEarnings / amount) * 100).toFixed(1) + '% of transaction');
}

// Run tests
testFeeCalculation();

// Test endpoint if backend is running
setTimeout(() => {
  testFinancialSettings();
}, 2000);
