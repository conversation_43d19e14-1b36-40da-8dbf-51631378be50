/**
 * Utility functions for calculating fee breakdowns in the payment system
 */

/**
 * Calculate comprehensive fee breakdown for a transaction
 * @param {number} amount - The original transaction amount
 * @param {object} settings - Settings object containing fee percentages
 * @returns {object} Detailed fee breakdown
 */
export const calculateFeeBreakdown = (amount, settings) => {
  const platformCommissionPercentage = settings?.platformCommissionPercentage || 5;
  const stripeProcessingFeePercentage = settings?.stripeProcessingFeePercentage || 2.9;
  const stripeFixedFee = settings?.stripeFixedFee || 0.30;

  // Calculate platform commission (deducted from seller earnings)
  const platformCommission = amount * (platformCommissionPercentage / 100);
  
  // Calculate Stripe fees (percentage + fixed fee, deducted from seller earnings)
  const stripePercentageFee = amount * (stripeProcessingFeePercentage / 100);
  const stripeTotalFee = stripePercentageFee + stripeFixedFee;
  
  // Calculate seller earnings
  const sellerEarningsBeforeStripeFees = amount - platformCommission;
  const finalSellerEarnings = sellerEarningsBeforeStripeFees - stripeTotalFee;
  
  // Ensure no negative earnings
  const adjustedFinalEarnings = Math.max(0, finalSellerEarnings);

  return {
    originalAmount: Math.round(amount * 100) / 100,
    platformCommission: Math.round(platformCommission * 100) / 100,
    platformCommissionPercentage,
    stripeFee: Math.round(stripeTotalFee * 100) / 100,
    stripePercentageFee: Math.round(stripePercentageFee * 100) / 100,
    stripeFixedFee: Math.round(stripeFixedFee * 100) / 100,
    stripeProcessingFeePercentage,
    sellerEarningsBeforeStripeFees: Math.round(sellerEarningsBeforeStripeFees * 100) / 100,
    finalSellerEarnings: Math.round(adjustedFinalEarnings * 100) / 100,
    buyerPaysAmount: Math.round(amount * 100) / 100, // Buyer always pays the full amount
    totalFeesDeducted: Math.round((platformCommission + stripeTotalFee) * 100) / 100
  };
};

/**
 * Format fee breakdown for display in UI components
 * @param {object} feeBreakdown - Result from calculateFeeBreakdown
 * @returns {object} Formatted breakdown for UI display
 */
export const formatFeeBreakdownForDisplay = (feeBreakdown) => {
  return {
    originalAmount: `$${feeBreakdown.originalAmount.toFixed(2)}`,
    platformCommission: `$${feeBreakdown.platformCommission.toFixed(2)}`,
    platformCommissionWithPercentage: `$${feeBreakdown.platformCommission.toFixed(2)} (${feeBreakdown.platformCommissionPercentage}%)`,
    stripeFee: `$${feeBreakdown.stripeFee.toFixed(2)}`,
    stripeFeeWithPercentage: `$${feeBreakdown.stripeFee.toFixed(2)} (${feeBreakdown.stripeProcessingFeePercentage}% + $${feeBreakdown.stripeFixedFee.toFixed(2)})`,
    sellerEarningsBeforeStripeFees: `$${feeBreakdown.sellerEarningsBeforeStripeFees.toFixed(2)}`,
    finalSellerEarnings: `$${feeBreakdown.finalSellerEarnings.toFixed(2)}`,
    buyerPaysAmount: `$${feeBreakdown.buyerPaysAmount.toFixed(2)}`,
    totalFeesDeducted: `$${feeBreakdown.totalFeesDeducted.toFixed(2)}`
  };
};

/**
 * Calculate seller earnings percentage after all fees
 * @param {number} amount - Original transaction amount
 * @param {object} settings - Settings object containing fee percentages
 * @returns {number} Percentage of original amount that seller receives
 */
export const calculateSellerEarningsPercentage = (amount, settings) => {
  const breakdown = calculateFeeBreakdown(amount, settings);
  return Math.round((breakdown.finalSellerEarnings / breakdown.originalAmount) * 100 * 100) / 100;
};

/**
 * Validate fee settings to ensure they don't result in negative seller earnings
 * @param {object} settings - Settings object containing fee percentages
 * @param {number} minAmount - Minimum transaction amount to test
 * @returns {object} Validation result with warnings
 */
export const validateFeeSettings = (settings, minAmount = 5) => {
  const breakdown = calculateFeeBreakdown(minAmount, settings);
  const warnings = [];
  
  if (breakdown.finalSellerEarnings <= 0) {
    warnings.push(`Current fee structure results in zero or negative seller earnings for transactions of $${minAmount}`);
  }
  
  if (breakdown.totalFeesDeducted >= breakdown.originalAmount * 0.5) {
    warnings.push(`Total fees exceed 50% of transaction amount`);
  }
  
  const sellerPercentage = calculateSellerEarningsPercentage(minAmount, settings);
  if (sellerPercentage < 50) {
    warnings.push(`Sellers receive less than 50% of transaction amount (${sellerPercentage}%)`);
  }
  
  return {
    isValid: warnings.length === 0,
    warnings,
    sellerEarningsPercentage: sellerPercentage,
    breakdown
  };
};
