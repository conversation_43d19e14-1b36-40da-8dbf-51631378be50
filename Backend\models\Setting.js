const mongoose = require('mongoose');

const settingSchema = new mongoose.Schema({
  general: {
    siteName: { type: String, default: "XOSportsHub" },
    siteLogo: { type: String }, // Cloudinary URL or local path
    siteFavicon: { type: String }, // Cloudinary URL or local path for favicon
    contactEmail: { type: String },
    contactPhone: { type: String },
    address: { type: String },
    supportLink: { type: String }, // For help center or live chat
    socialLinks: {
      facebook: { type: String, trim: true },
      twitter: { type: String, trim: true },
      instagram: { type: String, trim: true }
    }
  },
  launch: {
    launchDateTime: {
      type: Date,
      default: null // null means site is already launched
    },
    maintenanceMode: {
      type: Boolean,
      default: false
    }
  },
  financial: {
    platformCommissionPercentage: { type: Number, default: 5 }, // Platform commission percentage
    stripeProcessingFeePercentage: { type: Number, default: 2.9 }, // Stripe percentage fee
    stripeFixedFee: { type: Number, default: 0.30 }, // Stripe fixed fee in USD
    lastStripeFeesUpdate: { type: Date, default: Date.now }, // Track when fees were last updated
    // Helper method to calculate Stripe fees
    calculateStripeFee: function(amount) {
      const percentageFee = amount * (this.stripeProcessingFeePercentage / 100);
      const totalFee = percentageFee + this.stripeFixedFee;
      return Math.round(totalFee * 100) / 100; // Round to 2 decimal places
    }
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }
});

// Update the updatedAt field before saving
settingSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to calculate comprehensive fee breakdown
settingSchema.statics.calculateFeeBreakdown = function(amount, settings) {
  const platformCommission = amount * (settings.financial.platformCommissionPercentage / 100);
  const stripePercentageFee = amount * (settings.financial.stripeProcessingFeePercentage / 100);
  const stripeTotalFee = stripePercentageFee + settings.financial.stripeFixedFee;
  const sellerEarningsBeforeStripeFees = amount - platformCommission;
  const finalSellerEarnings = sellerEarningsBeforeStripeFees - stripeTotalFee;

  return {
    originalAmount: Math.round(amount * 100) / 100,
    platformCommission: Math.round(platformCommission * 100) / 100,
    stripeFee: Math.round(stripeTotalFee * 100) / 100,
    sellerEarningsBeforeStripeFees: Math.round(sellerEarningsBeforeStripeFees * 100) / 100,
    finalSellerEarnings: Math.round(finalSellerEarnings * 100) / 100,
    buyerPaysAmount: Math.round(amount * 100) / 100 // Buyer always pays the full amount
  };
};

// Ensure only one settings document exists (singleton pattern)
settingSchema.statics.getSingleton = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

// Check if site is in restricted mode (pre-launch or maintenance)
settingSchema.methods.isRestricted = function() {
  // Check maintenance mode
  if (this.launch?.maintenanceMode) {
    return { restricted: true, reason: 'maintenance' };
  }

  // Check if launch date is set and hasn't passed yet
  if (this.launch?.launchDateTime) {
    const now = new Date();
    const launchDate = new Date(this.launch.launchDateTime);
    if (now < launchDate) {
      return { restricted: true, reason: 'pre-launch', launchDate: launchDate };
    }
  }

  return { restricted: false };
};

// Static method to check restriction status
settingSchema.statics.checkRestrictionStatus = async function() {
  const settings = await this.getSingleton();
  return settings.isRestricted();
};

module.exports = mongoose.model("Setting", settingSchema);
